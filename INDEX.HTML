<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page de Connexion - Style Moderne</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 500px;
            width: 100%;
            animation: slideIn 0.8s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        h1 {
            color: #333;
            font-size: 2.5rem;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        h2 {
            color: #666;
            font-size: 1.5rem;
            margin-bottom: 20px;
            font-weight: 300;
        }

        .greeting {
            color: #555;
            font-size: 1.2rem;
            margin-bottom: 30px;
            padding: 15px;
            background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
            border-radius: 10px;
            color: white;
            box-shadow: 0 5px 15px rgba(240, 147, 251, 0.3);
        }

        .welcome-card {
            background: linear-gradient(45deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            color: white;
            box-shadow: 0 10px 25px rgba(79, 172, 254, 0.3);
            transform: perspective(1000px) rotateX(5deg);
            transition: transform 0.3s ease;
        }

        .welcome-card:hover {
            transform: perspective(1000px) rotateX(0deg) scale(1.02);
        }

        .decorative-line {
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
            border-radius: 2px;
            margin: 20px 0;
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% {
                background-position: -200px 0;
            }
            100% {
                background-position: 200px 0;
            }
        }

        .icon {
            font-size: 3rem;
            margin-bottom: 20px;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        @media (max-width: 600px) {
            .container {
                padding: 30px 20px;
                margin: 10px;
            }

            h1 {
                font-size: 2rem;
            }

            h2 {
                font-size: 1.2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">🌟</div>
        <h1>SALAM</h1>
        <div class="decorative-line"></div>
        <h2>SALAM SSS</h2>
        <div class="welcome-card">
            <div class="greeting">Wa 3alikom السلام</div>
        </div>
        <div style="margin-top: 30px; color: #888; font-size: 0.9rem;">
            ✨ Page stylée avec CSS moderne ✨
        </div>
    </div>
</body>
</html>
